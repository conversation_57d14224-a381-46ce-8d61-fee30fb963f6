<template>
  <div class="p-4">
    <div class="bg-white p-4 rounded-lg shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium">设备管理</h2>
        <div class="flex gap-2">
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增设备
          </a-button>
          <a-button type="primary" @click="handleDownloadTemplate">
            <template #icon>
              <DownloadOutlined />
            </template>
            下载模板
          </a-button>
          <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="mb-4">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch" class="search-form">
          <a-form-item label="设备名称">
            <a-input v-model:value="searchForm.deviceName" placeholder="请输入设备名称" allow-clear />
          </a-form-item>
          <a-form-item label="设备型号">
            <a-input v-model:value="searchForm.models" placeholder="请输入设备型号" allow-clear />
          </a-form-item>
          <a-form-item label="模型名称">
            <a-input v-model:value="searchForm.code" placeholder="请输入模型名称" allow-clear />
          </a-form-item>
          <a-form-item label="类型">
            <a-input v-model:value="searchForm.type" placeholder="请输入类型" allow-clear />
          </a-form-item>
          <a-form-item label="局楼">
            <a-input v-model:value="searchForm.building" placeholder="请输入局楼" allow-clear />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">查询</a-button>
            <a-button @click="handleReset" style="margin-left: 8px">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        size="middle"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'size'">
            {{ formatSize(record) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-popconfirm title="确定要删除这个设备吗？" @confirm="handleDelete([record.id])">
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 批量操作 -->
      <div v-if="selectedRowKeys.length > 0" class="mt-4">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-popconfirm title="确定要删除选中的设备吗？" @confirm="handleDelete(selectedRowKeys)">
            <a-button type="primary" danger size="small">批量删除</a-button>
          </a-popconfirm>
        </a-space>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <BasicModal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑设备' : '新增设备'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitLoading"
      width="900px"
      class="device-modal"
    >
      <BasicForm @register="registerDeviceForm" />
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
  // 组件引入
  import { ref, reactive, onMounted, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
  import {
    getAssetDeviceList,
    addAssetDevice,
    updateAssetDevice,
    deleteAssetDevice,
    type AssetDeviceModel,
    type AssetDeviceParams,
  } from '/@/api/asset/device';
  import { getRoomList, type RoomModel } from '/@/api/asset/room';
  import { BasicModal } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { safeSetFieldsValue } from '/@/utils/form';
  import { downloadFile } from '/@/api/common/api';
  import { useListPage } from '/@/hooks/system/useListPage';

  // 常量定义
  const DEFAULT_PAGE_SIZE = 10;
  const MIN_DIMENSION = 0;
  const DECIMAL_PRECISION = 2;

  // 搜索表单类型
  interface SearchForm {
    deviceName: string;
    models: string;
    code: string;
    type: string;
    building: string;
  }

  // 扩展设备模型类型，添加房间名称字段
  interface ExtendedAssetDeviceModel extends AssetDeviceModel {
    roomName?: string;
  }

  // 响应式数据
  const loading = ref<boolean>(false);
  const submitLoading = ref<boolean>(false);
  const tableData = ref<ExtendedAssetDeviceModel[]>([]);
  const selectedRowKeys = ref<number[]>([]);
  const modalVisible = ref<boolean>(false);
  const isEdit = ref<boolean>(false);
  const roomOptions = ref<{ label: string; value: number }[]>([]);

  // 使用 useListPage 获取导入方法
  const { onImportXls } = useListPage({
    tableProps: {}, // 添加必需的tableProps参数
    importConfig: {
      url: '/assetDevice/import',
      success: () => {
        loadData();
        message.success('导入成功');
      },
    },
  });

  // 搜索表单
  const searchForm = reactive<SearchForm>({
    deviceName: '',
    models: '',
    code: '',
    type: '',
    building: '',
  });

  // 设备表单配置
  const deviceFormSchema: FormSchema[] = [
    {
      label: '',
      field: 'id',
      component: 'Input',
      show: false,
    },
    {
      label: '设备名称',
      field: 'deviceName',
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: '请输入设备名称',
        maxlength: 100,
      },
      colProps: { span: 12 },
      rules: [
        { required: true, message: '请输入设备名称' },
        { min: 2, max: 100, message: '设备名称长度应在2-100个字符之间' },
      ],
    },
    {
      label: '设备型号',
      field: 'models',
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: '请输入设备型号',
        maxlength: 50,
      },
      colProps: { span: 12 },
      rules: [
        { required: true, message: '请输入设备型号' },
        { min: 1, max: 50, message: '设备型号长度应在1-50个字符之间' },
      ],
    },
    {
      label: '模型名称',
      field: 'code',
      component: 'Input',
      componentProps: {
        placeholder: '请输入模型名称',
        maxlength: 50,
      },
      colProps: { span: 12 },
      rules: [{ max: 50, message: '模型名称长度不能超过50个字符' }],
    },
    {
      label: '类型',
      field: 'type',
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: '请输入类型',
        maxlength: 50,
      },
      colProps: { span: 12 },
      rules: [
        { required: true, message: '请输入设备类型' },
        { min: 1, max: 50, message: '设备类型长度应在1-50个字符之间' },
      ],
    },
    {
      label: '房间',
      field: 'roomId',
      required: true,
      component: 'Select',
      componentProps: () => ({
        placeholder: '请选择房间',
        style: { width: '100%' },
        options: roomOptions.value,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option?.label?.toLowerCase().includes(input.toLowerCase());
        },
      }),
      colProps: { span: 12 },
      rules: [{ required: true, message: '请选择房间' }],
    },
    {
      label: '局楼',
      field: 'building',
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: '请输入局楼',
        maxlength: 100,
      },
      colProps: { span: 12 },
      rules: [
        { required: true, message: '请输入局楼' },
        { min: 1, max: 100, message: '局楼名称长度应在1-100个字符之间' },
      ],
    },
    {
      label: '厂家',
      field: 'manufacturer',
      component: 'Input',
      componentProps: {
        placeholder: '请输入厂家',
        maxlength: 100,
      },
      colProps: { span: 12 },
      rules: [{ max: 100, message: '厂家名称长度不能超过100个字符' }],
    },
    {
      label: '出厂日期',
      field: 'productionDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择出厂日期',
        style: { width: '100%' },
        disabledDate: (current: any) => {
          // 不能选择未来的日期
          return current && current > new Date();
        },
      },
      colProps: { span: 12 },
    },
    {
      label: '启用日期',
      field: 'enableDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择启用日期',
        style: { width: '100%' },
      },
      colProps: { span: 12 },
    },
    {
      label: '出厂编号',
      field: 'productionNum',
      component: 'Input',
      componentProps: {
        placeholder: '请输入出厂编号',
        maxlength: 50,
      },
      colProps: { span: 24 },
      rules: [{ max: 50, message: '出厂编号长度不能超过50个字符' }],
    },
    {
      label: '长度(cm)',
      field: 'deviceLong',
      component: 'InputNumber',
      componentProps: {
        min: MIN_DIMENSION,
        max: 10000,
        precision: DECIMAL_PRECISION,
        placeholder: '长度',
        style: { width: '100%' },
      },
      colProps: { span: 8 },
      rules: [{ type: 'number', min: 0, max: 10000, message: '长度应在0-10000cm之间' }],
    },
    {
      label: '宽度(cm)',
      field: 'deviceWide',
      component: 'InputNumber',
      componentProps: {
        min: MIN_DIMENSION,
        max: 10000,
        precision: DECIMAL_PRECISION,
        placeholder: '宽度',
        style: { width: '100%' },
      },
      colProps: { span: 8 },
      rules: [{ type: 'number', min: 0, max: 10000, message: '宽度应在0-10000cm之间' }],
    },
    {
      label: '高度(cm)',
      field: 'deviceHigh',
      component: 'InputNumber',
      componentProps: {
        min: MIN_DIMENSION,
        max: 10000,
        precision: DECIMAL_PRECISION,
        placeholder: '高度',
        style: { width: '100%' },
      },
      colProps: { span: 8 },
      rules: [{ type: 'number', min: 0, max: 10000, message: '高度应在0-10000cm之间' }],
    },
    {
      label: '重量(kg)',
      field: 'deviceWeight',
      component: 'InputNumber',
      componentProps: {
        min: MIN_DIMENSION,
        max: 100000,
        precision: DECIMAL_PRECISION,
        placeholder: '重量',
        style: { width: '100%' },
      },
      colProps: { span: 24 },
      rules: [{ type: 'number', min: 0, max: 100000, message: '重量应在0-100000kg之间' }],
    },
  ];

  // 表单配置
  const [registerDeviceForm, { resetFields, setFieldsValue, validate }] = useForm({
    schemas: deviceFormSchema,
    showActionButtonGroup: false,
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
    baseColProps: { span: 24 },
    labelWidth: 100, // 固定label宽度为100px
  });

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 表格列配置
  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      key: 'deviceName',
      width: 150,
    },
    {
      title: '设备型号',
      dataIndex: 'models',
      key: 'models',
      width: 120,
    },
    {
      title: '模型名称',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: '房间',
      dataIndex: 'roomName',
      key: 'roomName',
      width: 120,
    },
    {
      title: '局楼',
      dataIndex: 'building',
      key: 'building',
      width: 120,
    },
    {
      title: '厂家',
      dataIndex: 'manufacturer',
      key: 'manufacturer',
      width: 120,
    },
    {
      title: '尺寸(长×宽×高)',
      key: 'size',
      width: 150,
    },
    {
      title: '重量(kg)',
      dataIndex: 'deviceWeight',
      key: 'deviceWeight',
      width: 100,
    },
    {
      title: '出厂编号',
      dataIndex: 'productionNum',
      key: 'productionNum',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
    },
  ];

  // 计算属性和工具函数
  const formatSize = (record: AssetDeviceModel): string => {
    const { deviceLong, deviceWide, deviceHigh } = record;
    if (!deviceLong || !deviceWide || !deviceHigh) {
      return '-';
    }
    return `${deviceLong}×${deviceWide}×${deviceHigh}cm`;
  };

  // 构建查询参数
  const buildParams = (): AssetDeviceParams => {
    return {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      deviceName: searchForm.deviceName || undefined,
      models: searchForm.models || undefined,
      code: searchForm.code || undefined,
      type: searchForm.type || undefined,
      building: searchForm.building || undefined,
    };
  };

  // 重置搜索表单
  const resetSearchForm = (): void => {
    searchForm.deviceName = '';
    searchForm.models = '';
    searchForm.code = '';
    searchForm.type = '';
    searchForm.building = '';
  };

  // 加载房间选项数据
  const loadRoomOptions = async (): Promise<void> => {
    try {
      const result = await getRoomList({ current: 1, size: 1000 }); // 获取所有房间
      roomOptions.value = result.records.map((room: RoomModel) => ({
        label: `${room.roomName} (${room.floors}楼)`,
        value: room.id,
      }));
    } catch (error) {
      console.error('加载房间数据失败:', error);
      message.error('加载房间数据失败');
    }
  };

  // 加载数据
  const loadData = async (): Promise<void> => {
    try {
      loading.value = true;
      const params = buildParams();
      const result = await getAssetDeviceList(params);

      // 为每个设备添加房间名称
      tableData.value = result.records.map((device: AssetDeviceModel): ExtendedAssetDeviceModel => {
        const room = roomOptions.value.find((room) => room.value === device.roomId);
        return {
          ...device,
          roomName: room ? room.label : `房间ID: ${device.roomId}`,
        };
      });
      pagination.total = result.total;
    } catch (error) {
      console.error('加载设备数据失败:', error);
      message.error('加载数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 搜索
  const handleSearch = (): void => {
    pagination.current = 1;
    loadData();
  };

  // 重置搜索
  const handleReset = (): void => {
    resetSearchForm();
    pagination.current = 1;
    loadData();
  };

  // 刷新
  const handleRefresh = (): void => {
    loadData();
    message.success('数据已刷新');
  };

  // 表格变化
  const handleTableChange = (pag: any): void => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadData();
  };

  // 选择变化
  const onSelectChange = (keys: number[]): void => {
    selectedRowKeys.value = keys;
  };

  // 新增
  const handleAdd = async (): Promise<void> => {
    isEdit.value = false;
    modalVisible.value = true;
    resetFields();
    // 确保房间选项已加载
    if (roomOptions.value.length === 0) {
      await loadRoomOptions();
    }
  };

  // 编辑
  const handleEdit = async (record: ExtendedAssetDeviceModel): Promise<void> => {
    isEdit.value = true;
    modalVisible.value = true;
    // 确保房间选项已加载
    if (roomOptions.value.length === 0) {
      await loadRoomOptions();
    }
    try {
      await safeSetFieldsValue(setFieldsValue, record);
    } catch (error) {
      console.error('设置表单值失败:', error);
      message.error('加载编辑数据失败');
    }
  };

  // 提交表单
  const handleSubmit = async (): Promise<void> => {
    try {
      const values = await validate();
      submitLoading.value = true;

      if (isEdit.value) {
        // 确保ID字段为数字类型
        const updateData: AssetDeviceModel = {
          ...values,
          id: typeof values.id === 'string' ? parseInt(values.id, 10) : values.id,
        };

        // 验证ID是否有效
        if (!updateData.id || isNaN(updateData.id)) {
          throw new Error('无效的设备ID');
        }

        await updateAssetDevice(updateData);
      } else {
        const { id, ...addData } = values;
        await addAssetDevice(addData);
      }

      message.success(isEdit.value ? '修改成功' : '新增成功');
      modalVisible.value = false;
      loadData();
    } catch (error) {
      console.error('提交失败:', error);
      const errorMessage = error instanceof Error ? error.message : '操作失败';
      message.error(errorMessage);
    } finally {
      submitLoading.value = false;
    }
  };

  // 取消
  const handleCancel = (): void => {
    modalVisible.value = false;
    resetFields();
  };

  // 删除
  const handleDelete = async (ids: number[]): Promise<void> => {
    if (!ids || ids.length === 0) {
      message.warning('请选择要删除的设备');
      return;
    }

    try {
      await deleteAssetDevice(ids);
      message.success('删除成功');
      selectedRowKeys.value = [];
      loadData();
    } catch (error) {
      console.error('删除失败:', error);
      const errorMessage = error instanceof Error ? error.message : '删除失败';
      message.error(errorMessage);
    }
  };

  // 下载模板
  const handleDownloadTemplate = async (): Promise<void> => {
    try {
      await downloadFile('/assetDevice/downloadTemplate', '资产设备导入模板.xlsx');
      message.success('模板下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      const errorMessage = error instanceof Error ? error.message : '下载模板失败';
      message.error(errorMessage);
    }
  };

  // 挂载时加载数据
  onMounted(async () => {
    await loadRoomOptions(); // 先加载房间数据
    loadData(); // 再加载设备数据
  });
</script>

<style scoped lang="less">
  .search-form {
    :deep(.ant-form-item) {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      margin-right: 16px;

      .ant-form-item-label {
        flex: none;
        width: auto;
        margin-right: 8px;

        label {
          white-space: nowrap;
        }
      }

      .ant-form-item-control {
        flex: 1;
        min-width: 0;
      }
    }
  }

  // 设备弹窗样式优化
  :deep(.device-modal) {
    .ant-modal-body {
      padding: 24px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .ant-form {
      .ant-row {
        margin-left: -8px;
        margin-right: -8px;
      }

      .ant-col {
        padding-left: 8px;
        padding-right: 8px;
      }

      .ant-form-item {
        margin-bottom: 16px;
        transition: all 0.2s;
        display: flex;
        align-items: center;

        .ant-form-item-label {
          padding-bottom: 0;
          padding-right: 12px;
          text-align: right;
          width: 100px !important;
          min-width: 100px !important;
          flex: none;

          label {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            position: relative;
            white-space: nowrap;
            width: 100%;
            display: inline-block;

            &::after {
              content: '';
              position: absolute;
              bottom: -2px;
              left: 0;
              width: 0;
              height: 2px;
              background: linear-gradient(90deg, #1890ff, #40a9ff);
              transition: width 0.3s ease;
            }
          }

          &:hover label::after {
            width: 100%;
          }
        }

        .ant-form-item-control {
          .ant-form-item-control-input {
            min-height: 32px;

            .ant-input,
            .ant-input-number,
            .ant-select-selector,
            .ant-picker {
              border-radius: 6px;
              transition: all 0.2s;
              border: 1px solid #d9d9d9;

              &:hover {
                border-color: #40a9ff;
                box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
              }

              &:focus,
              &.ant-select-focused .ant-select-selector,
              &.ant-picker-focused {
                border-color: #40a9ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              }
            }

            .ant-input-number {
              width: 100%;
            }
          }
        }

        // 必填字段标识
        &.ant-form-item-required {
          .ant-form-item-label label {
            &::before {
              content: '*';
              color: #ff4d4f;
              font-size: 14px;
              margin-right: 4px;
            }
          }
        }

        // 错误状态样式
        &.ant-form-item-has-error {
          .ant-form-item-explain {
            margin-top: 4px;
            font-size: 12px;
            color: #ff4d4f;
          }

          .ant-form-item-control-input {
            .ant-input,
            .ant-input-number,
            .ant-select-selector,
            .ant-picker {
              border-color: #ff4d4f;
              box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
            }
          }
        }

        // 成功状态样式
        &.ant-form-item-has-success {
          .ant-form-item-control-input {
            .ant-input,
            .ant-input-number,
            .ant-select-selector,
            .ant-picker {
              border-color: #52c41a;
            }
          }
        }
      }

      // 分组样式 - 基本信息
      .ant-form-item:nth-child(-n + 5) {
        background: rgba(24, 144, 255, 0.02);
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 3px solid #1890ff;
      }

      // 分组样式 - 日期信息
      .ant-form-item:nth-child(n + 6):nth-child(-n + 8) {
        background: rgba(82, 196, 26, 0.02);
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 3px solid #52c41a;
      }

      // 分组样式 - 尺寸信息
      .ant-form-item:nth-child(n + 9) {
        background: rgba(250, 173, 20, 0.02);
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 3px solid #faad14;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.device-modal) {
      .ant-modal {
        margin: 0;
        max-width: 100vw;
        top: 0;
      }

      .ant-modal-content {
        border-radius: 0;
      }

      .ant-form {
        .ant-col {
          flex: 0 0 100% !important;
          max-width: 100% !important;
        }

        .ant-form-item {
          display: block !important;

          .ant-form-item-label {
            text-align: left !important;
            padding-right: 0 !important;
            padding-bottom: 6px !important;
            width: auto !important;
            min-width: auto !important;
          }
        }
      }
    }
  }
</style>
